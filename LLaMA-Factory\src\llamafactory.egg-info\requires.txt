datasets<=3.6.0,>=2.16.0
accelerate<=1.7.0,>=1.3.0
peft<=0.15.2,>=0.14.0
trl<=0.9.6,>=0.8.6
tokenizers<=0.21.1,>=0.19.0
gradio<=5.31.0,>=4.38.0
scipy
einops
sentencepiece
tiktoken
protobuf
uvicorn
fastapi
sse-starlette
matplotlib>=3.7.0
fire
omegaconf
packaging
pyyaml
numpy<2.0.0
pydantic<=2.10.6
pandas>=2.0.0
av
librosa
tyro<0.9.0

[:sys_platform != "darwin"]
transformers!=4.46.*,!=4.47.*,!=4.48.0,!=4.52.0,<=4.52.4,>=4.45.0

[:sys_platform == "darwin"]
transformers!=4.46.*,!=4.47.*,!=4.48.0,!=4.52.0,<=4.51.3,>=4.45.0

[adam-mini]
adam-mini

[apollo]
apollo-torch

[aqlm]
aqlm[gpu]>=1.1.0

[badam]
badam>=1.2.1

[bitsandbytes]
bitsandbytes>=0.39.0

[deepspeed]
deepspeed<=0.16.9,>=0.10.0

[dev]
pre-commit
ruff
pytest
build

[eetq]
eetq

[galore]
galore-torch

[gptq]
optimum>=1.24.0
gptqmodel>=2.0.0

[hqq]
hqq

[liger-kernel]
liger-kernel>=0.5.5

[metrics]
nltk
jieba
rouge-chinese

[minicpm_v]
soundfile
torchvision
torchaudio
vector_quantize_pytorch
vocos
msgpack
referencing
jsonschema_specifications

[modelscope]
modelscope

[openmind]
openmind

[sglang]
sglang[srt]>=0.4.5
transformers==4.51.1

[swanlab]
swanlab

[torch]
torch>=2.0.0
torchvision>=0.15.0

[torch-npu]
torch==2.4.0
torch-npu==2.4.0.post2
decorator

[vllm]
vllm<=0.9.1,>=0.4.3
